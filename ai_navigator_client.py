"""
AI Navigator API Client with JWT Authentication
Handles authentication, token refresh, and API calls to the AI Navigator backend.
"""

import requests
import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
from config import config
from taxonomy_manager import TaxonomyManager, TaxonomyResult

class AINavigatorClient:
    def __init__(self, base_url: Optional[str] = None):
        self.base_url = (base_url or config.api.ai_navigator_base_url).rstrip('/')
        self.api_base_url = f"{self.base_url}/api"
        self.admin_email = config.api.ai_navigator_admin_email
        self.admin_password = config.api.ai_navigator_admin_password
        self.access_token = None
        self.token_expiry = None
        self.logger = logging.getLogger(__name__)

        # Initialize taxonomy manager
        self.taxonomy_manager = None

        # Validate required configuration
        if not self.admin_email or not self.admin_password:
            raise ValueError("AI Navigator admin credentials not configured. Please set AI_NAVIGATOR_ADMIN_EMAIL and AI_NAVIGATOR_ADMIN_PASSWORD environment variables.")
        
    def _initialize_taxonomy_manager(self):
        """Initialize taxonomy manager with current auth token"""
        if not self._is_token_valid():
            self._refresh_token()
        
        if self.access_token and not self.taxonomy_manager:
            self.taxonomy_manager = TaxonomyManager(
                base_url=self.base_url,
                auth_token=self.access_token
            )
            self.logger.info("✅ Taxonomy manager initialized")
    
    def process_taxonomy_suggestions(self, 
                                   suggested_categories: List[str] = None,
                                   suggested_features: List[str] = None, 
                                   suggested_tags: List[str] = None) -> TaxonomyResult:
        """
        Process taxonomy suggestions and return UUIDs for entity association.
        
        This method handles the complete flow:
        1. Check if categories/features/tags exist
        2. Create missing taxonomy items
        3. Return UUIDs for entity association
        
        Args:
            suggested_categories: List of category names
            suggested_features: List of feature names  
            suggested_tags: List of tag names
            
        Returns:
            TaxonomyResult with UUIDs and any errors
        """
        # Initialize taxonomy manager if needed
        if not self.taxonomy_manager:
            self._initialize_taxonomy_manager()
        
        if not self.taxonomy_manager:
            self.logger.error("❌ Failed to initialize taxonomy manager")
            return TaxonomyResult(categories=[], features=[], tags=[], errors=["Failed to initialize taxonomy manager"])
        
        # Update taxonomy manager's auth token if needed
        if not self._is_token_valid():
            self._refresh_token()
            if self.access_token:
                self.taxonomy_manager.auth_token = self.access_token
                self.taxonomy_manager.session.headers.update({
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                })
        
        # Process taxonomy suggestions
        return self.taxonomy_manager.process_taxonomy_suggestions(
            suggested_categories=suggested_categories,
            suggested_features=suggested_features,
            suggested_tags=suggested_tags
        )
    
    def create_entity_with_taxonomy(self, 
                                  entity_data: Dict[str, Any],
                                  suggested_categories: List[str] = None,
                                  suggested_features: List[str] = None,
                                  suggested_tags: List[str] = None) -> Optional[Dict[str, Any]]:
        """
        Create entity with automatic taxonomy management.
        
        This method:
        1. Processes taxonomy suggestions (creates missing items)
        2. Adds taxonomy UUIDs to entity data
        3. Creates the entity with proper associations
        
        Args:
            entity_data: Base entity data
            suggested_categories: List of category names
            suggested_features: List of feature names
            suggested_tags: List of tag names
            
        Returns:
            Created entity data or None if failed
        """
        try:
            self.logger.info(f"Creating entity with taxonomy: {entity_data.get('name', 'Unknown')}")
            
            # Process taxonomy suggestions
            taxonomy_result = self.process_taxonomy_suggestions(
                suggested_categories=suggested_categories,
                suggested_features=suggested_features,
                suggested_tags=suggested_tags
            )
            
            # Log taxonomy processing results
            if taxonomy_result.errors:
                self.logger.warning(f"Taxonomy processing errors: {taxonomy_result.errors}")
            
            self.logger.info(f"Taxonomy processed: {len(taxonomy_result.categories)} categories, "
                           f"{len(taxonomy_result.features)} features, {len(taxonomy_result.tags)} tags")
            
            # Get UUID lists
            category_uuids, feature_uuids, tag_uuids = self.taxonomy_manager.get_taxonomy_uuids(taxonomy_result)
            
            # Add taxonomy UUIDs to entity data
            if category_uuids:
                entity_data['category_ids'] = category_uuids
                self.logger.info(f"Added {len(category_uuids)} category UUIDs to entity")
            
            if feature_uuids:
                entity_data['feature_ids'] = feature_uuids
                self.logger.info(f"Added {len(feature_uuids)} feature UUIDs to entity")
            
            if tag_uuids:
                entity_data['tag_ids'] = tag_uuids
                self.logger.info(f"Added {len(tag_uuids)} tag UUIDs to entity")
            
            # Create entity with taxonomy associations
            result = self.create_entity(entity_data)
            
            if result:
                self.logger.info(f"✅ Successfully created entity with taxonomy: {entity_data.get('name', 'Unknown')}")
            else:
                self.logger.error(f"❌ Failed to create entity: {entity_data.get('name', 'Unknown')}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error creating entity with taxonomy: {str(e)}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return None

    def get_taxonomy_cache_stats(self) -> Dict[str, int]:
        """Get taxonomy cache statistics for monitoring"""
        if self.taxonomy_manager:
            return self.taxonomy_manager.get_cache_stats()
        return {'categories_cached': 0, 'features_cached': 0, 'tags_cached': 0}
    
    def refresh_taxonomy_caches(self):
        """Refresh all taxonomy caches"""
        if self.taxonomy_manager:
            self.taxonomy_manager.refresh_caches()
            self.logger.info("✅ Taxonomy caches refreshed")
    
    def _get_headers(self) -> Dict[str, str]:
        """Get headers with current access token"""
        if not self._is_token_valid():
            self._refresh_token()
        
        return {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
    
    def _is_token_valid(self) -> bool:
        """Check if current token is valid and not expired"""
        if not self.access_token or not self.token_expiry:
            return False
        
        # Add 5 minute buffer before expiry
        return datetime.now() < (self.token_expiry - timedelta(minutes=5))
    
    def _refresh_token(self) -> bool:
        """Login and refresh the JWT token"""
        try:
            login_data = {
                "email": self.admin_email,
                "password": self.admin_password
            }

            self.logger.info(f"Attempting to login to AI Navigator API at: {self.base_url}/auth/login")
            self.logger.debug(f"Login email: {self.admin_email}")

            response = requests.post(
                f"{self.base_url}/auth/login",
                json=login_data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )

            self.logger.info(f"Login response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                session = data.get('session', {})
                self.access_token = session.get('access_token')
                # Use expires_in from the session data
                expires_in = session.get('expires_in', 3600)
                self.token_expiry = datetime.now() + timedelta(seconds=expires_in)

                if self.access_token:
                    self.logger.info("✅ Successfully refreshed AI Navigator API token")
                    self.logger.debug(f"Token expires in {expires_in} seconds")
                    return True
                else:
                    self.logger.error("❌ Login successful but no access token received")
                    self.logger.error(f"Session data: {json.dumps(session, indent=2)}")
                    return False
            else:
                self.logger.error(f"❌ Failed to login: {response.status_code}")
                try:
                    error_data = response.json()
                    self.logger.error(f"Login error response: {json.dumps(error_data, indent=2)}")
                except ValueError:
                    self.logger.error(f"Login error text: {response.text}")
                return False

        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"❌ Connection Error during login: {e}")
            self.logger.error(f"Check if AI Navigator API is accessible at: {self.base_url}")
            return False
        except requests.exceptions.Timeout as e:
            self.logger.error(f"❌ Timeout Error during login: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ Unexpected Error refreshing token: {str(e)}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def create_entity(self, entity_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a new entity via POST /entities"""
        try:
            # Log the request details for debugging
            self.logger.info(f"Creating entity: {entity_data.get('name', 'Unknown')} (URL: {entity_data.get('website_url', 'N/A')})")
            self.logger.debug(f"Request URL: {self.base_url}/entities")
            self.logger.debug(f"Entity data payload: {json.dumps(entity_data, indent=2)}")

            response = requests.post(
                f"{self.base_url}/entities",
                json=entity_data,
                headers=self._get_headers(),
                timeout=30
            )

            # Log the full response details
            self.logger.info(f"AI Navigator API Response: Status {response.status_code}")
            self.logger.debug(f"Response headers: {dict(response.headers)}")

            if response.status_code in [200, 201]:
                response_data = response.json()
                entity_id = response_data.get('id', 'Unknown ID')
                self.logger.info(f"✅ Successfully created entity: {entity_data.get('name', 'Unknown')} (ID: {entity_id})")
                return response_data
            else:
                # Detailed error logging for failed requests
                self.logger.error(f"❌ Failed to create entity: {entity_data.get('name', 'Unknown')}")
                self.logger.error(f"HTTP Status Code: {response.status_code}")
                self.logger.error(f"Response Headers: {dict(response.headers)}")

                try:
                    error_response = response.json()
                    self.logger.error(f"Error Response Body: {json.dumps(error_response, indent=2)}")

                    # Extract specific error details if available
                    if isinstance(error_response, dict):
                        if 'message' in error_response:
                            self.logger.error(f"Error Message: {error_response['message']}")
                        if 'errors' in error_response:
                            self.logger.error(f"Validation Errors: {json.dumps(error_response['errors'], indent=2)}")
                        if 'details' in error_response:
                            self.logger.error(f"Error Details: {json.dumps(error_response['details'], indent=2)}")

                except ValueError:
                    # Response is not JSON
                    self.logger.error(f"Raw Response Text: {response.text}")

                # Log the entity data that failed to save
                self.logger.error(f"Failed Entity Data: {json.dumps(entity_data, indent=2)}")
                return None

        except requests.exceptions.HTTPError as e:
            self.logger.error(f"❌ HTTP Error creating entity: {e}")
            self.logger.error(f"Response status: {e.response.status_code if e.response else 'N/A'}")
            self.logger.error(f"Response text: {e.response.text if e.response else 'N/A'}")
            return None
        except requests.exceptions.ConnectionError as e:
            self.logger.error(f"❌ Connection Error creating entity: {e}")
            self.logger.error(f"Check if AI Navigator API is accessible at: {self.base_url}")
            return None
        except requests.exceptions.Timeout as e:
            self.logger.error(f"❌ Timeout Error creating entity: {e}")
            return None
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ Request Error creating entity: {e}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Unexpected Error creating entity: {str(e)}")
            self.logger.error(f"Entity data: {json.dumps(entity_data, indent=2)}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    def get_categories(self) -> List[Dict[str, Any]]:
        """Get all categories"""
        try:
            response = requests.get(
                f"{self.base_url}/categories",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Failed to get categories: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error getting categories: {str(e)}")
            return []
    
    def get_tags(self) -> List[Dict[str, Any]]:
        """Get all tags"""
        try:
            response = requests.get(
                f"{self.base_url}/tags",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Failed to get tags: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error getting tags: {str(e)}")
            return []
    
    def get_features(self) -> List[Dict[str, Any]]:
        """Get all features"""
        try:
            response = requests.get(
                f"{self.base_url}/features",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Failed to get features: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error getting features: {str(e)}")
            return []
    
    def get_entity_types(self) -> List[Dict[str, Any]]:
        """Get all entity types"""
        try:
            response = requests.get(
                f"{self.base_url}/entity-types",
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                self.logger.error(f"Failed to get entity types: {response.status_code}")
                return []
                
        except Exception as e:
            self.logger.error(f"Error getting entity types: {str(e)}")
            return []
    
    def get_ai_tool_entity_type_id(self) -> Optional[str]:
        """Get the entity type ID for AI tools"""
        try:
            entity_types = self.get_entity_types()
            for entity_type in entity_types:
                if entity_type.get('slug') == 'ai-tool':
                    self.logger.info(f"Found AI Tool entity type ID: {entity_type['id']}")
                    return entity_type['id']
            
            self.logger.error("AI Tool entity type not found")
            return None
                
        except Exception as e:
            self.logger.error(f"Error getting AI tool entity type ID: {str(e)}")
            return None

    def check_entity_exists(self, website_url: str) -> Optional[Dict[str, Any]]:
        """Check if entity with given website URL already exists"""
        try:
            # Note: This assumes there's a search/filter endpoint
            # You may need to adjust based on actual API structure
            response = requests.get(
                f"{self.base_url}/entities",
                params={"website_url": website_url},
                headers=self._get_headers(),
                timeout=10
            )
            
            if response.status_code == 200:
                entities = response.json()
                if isinstance(entities, list) and entities:
                    return entities[0]  # Return first match
                elif isinstance(entities, dict) and entities.get('data'):
                    data = entities['data']
                    return data[0] if data else None
            
            return None
                
        except Exception as e:
            self.logger.error(f"Error checking entity existence: {str(e)}")
            return None