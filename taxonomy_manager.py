import logging
import requests
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

@dataclass
class TaxonomyItem:
    """Represents a taxonomy item with its UUID and metadata"""
    uuid: str
    name: str
    slug: str
    item_type: str  # 'category', 'feature', 'tag'
    
@dataclass
class TaxonomyResult:
    """Result of taxonomy processing"""
    categories: List[TaxonomyItem]
    features: List[TaxonomyItem] 
    tags: List[TaxonomyItem]
    errors: List[str]

class TaxonomyManager:
    """
    Comprehensive taxonomy management service for AI Navigator API.
    
    Handles the complete flow of:
    1. Checking if categories/features/tags exist
    2. Creating missing taxonomy items
    3. Returning UUIDs for entity association
    """
    
    # Fallback defaults to ensure API validation requirements are met
    FALLBACK_CATEGORY_ID = "e52307e3-46d3-4516-a8d0-5ad7fde336b6"  # Business & Productivity
    FALLBACK_TAG_ID = "954d898e-0860-41b0-89bf-073db78d4c19"  # Cloud-Based
    
    def __init__(self, base_url: str = "https://ai-nav.onrender.com", auth_token: str = None):
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.session = requests.Session()
        
        # Set up authentication headers
        if self.auth_token:
            self.session.headers.update({
                'Authorization': f'Bearer {self.auth_token}',
                'Content-Type': 'application/json'
            })
        
        # Cache for taxonomy items to avoid duplicate API calls
        self._category_cache: Dict[str, TaxonomyItem] = {}
        self._feature_cache: Dict[str, TaxonomyItem] = {}
        self._tag_cache: Dict[str, TaxonomyItem] = {}
        
        self.logger = logging.getLogger(__name__)
    
    def process_taxonomy_suggestions(self, 
                                   suggested_categories: List[str] = None,
                                   suggested_features: List[str] = None, 
                                   suggested_tags: List[str] = None) -> TaxonomyResult:
        """
        Process taxonomy suggestions and return UUIDs for entity association.
        
        Args:
            suggested_categories: List of category names
            suggested_features: List of feature names  
            suggested_tags: List of tag names
            
        Returns:
            TaxonomyResult with UUIDs and any errors
        """
        result = TaxonomyResult(categories=[], features=[], tags=[], errors=[])
        
        # Process categories
        if suggested_categories:
            for category_name in suggested_categories:
                try:
                    category_item = self._get_or_create_category(category_name)
                    if category_item:
                        result.categories.append(category_item)
                except Exception as e:
                    error_msg = f"Failed to process category '{category_name}': {str(e)}"
                    self.logger.error(error_msg)
                    result.errors.append(error_msg)
        
        # Process features
        if suggested_features:
            for feature_name in suggested_features:
                try:
                    feature_item = self._get_or_create_feature(feature_name)
                    if feature_item:
                        result.features.append(feature_item)
                except Exception as e:
                    error_msg = f"Failed to process feature '{feature_name}': {str(e)}"
                    self.logger.error(error_msg)
                    result.errors.append(error_msg)
        
        # Process tags
        if suggested_tags:
            for tag_name in suggested_tags:
                try:
                    tag_item = self._get_or_create_tag(tag_name)
                    if tag_item:
                        result.tags.append(tag_item)
                except Exception as e:
                    error_msg = f"Failed to process tag '{tag_name}': {str(e)}"
                    self.logger.error(error_msg)
                    result.errors.append(error_msg)
        
        # Ensure API validation requirements are met (at least 1 category and 1 tag)
        if len(result.categories) == 0:
            self.logger.warning("No categories found/created, adding fallback category")
            fallback_category = TaxonomyItem(
                uuid=self.FALLBACK_CATEGORY_ID,
                name="Business & Productivity",
                slug="business-productivity",
                item_type="category"
            )
            result.categories.append(fallback_category)
        
        if len(result.tags) == 0:
            self.logger.warning("No tags found/created, adding fallback tag")
            fallback_tag = TaxonomyItem(
                uuid=self.FALLBACK_TAG_ID,
                name="Cloud-Based",
                slug="cloud-based",
                item_type="tag"
            )
            result.tags.append(fallback_tag)
        
        self.logger.info(f"Taxonomy processing complete: {len(result.categories)} categories, "
                        f"{len(result.features)} features, {len(result.tags)} tags, "
                        f"{len(result.errors)} errors")
        
        return result
    
    def _get_or_create_category(self, category_name: str) -> Optional[TaxonomyItem]:
        """Get existing category or create new one"""
        # Check cache first
        cache_key = category_name.lower().strip()
        if cache_key in self._category_cache:
            return self._category_cache[cache_key]
        
        # Check if category exists
        existing_category = self._find_existing_category(category_name)
        if existing_category:
            self._category_cache[cache_key] = existing_category
            return existing_category
        
        # Create new category
        new_category = self._create_category(category_name)
        if new_category:
            self._category_cache[cache_key] = new_category
        
        return new_category
    
    def _get_or_create_feature(self, feature_name: str) -> Optional[TaxonomyItem]:
        """Get existing feature or create new one"""
        # Check cache first
        cache_key = feature_name.lower().strip()
        if cache_key in self._feature_cache:
            return self._feature_cache[cache_key]
        
        # Check if feature exists
        existing_feature = self._find_existing_feature(feature_name)
        if existing_feature:
            self._feature_cache[cache_key] = existing_feature
            return existing_feature
        
        # Create new feature
        new_feature = self._create_feature(feature_name)
        if new_feature:
            self._feature_cache[cache_key] = new_feature
        
        return new_feature
    
    def _get_or_create_tag(self, tag_name: str) -> Optional[TaxonomyItem]:
        """Get existing tag or create new one"""
        # Check cache first
        cache_key = tag_name.lower().strip()
        if cache_key in self._tag_cache:
            return self._tag_cache[cache_key]
        
        # Check if tag exists
        existing_tag = self._find_existing_tag(tag_name)
        if existing_tag:
            self._tag_cache[cache_key] = existing_tag
            return existing_tag
        
        # Create new tag
        new_tag = self._create_tag(tag_name)
        if new_tag:
            self._tag_cache[cache_key] = new_tag
        
        return new_tag
    
    def _find_existing_category(self, category_name: str) -> Optional[TaxonomyItem]:
        """Find existing category by name"""
        try:
            response = self.session.get(f"{self.base_url}/categories")
            response.raise_for_status()
            
            categories = response.json()
            for category in categories:
                if category['name'].lower().strip() == category_name.lower().strip():
                    return TaxonomyItem(
                        uuid=category['id'],
                        name=category['name'],
                        slug=category['slug'],
                        item_type='category'
                    )
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding category '{category_name}': {str(e)}")
            return None
    
    def _find_existing_feature(self, feature_name: str) -> Optional[TaxonomyItem]:
        """Find existing feature by name"""
        try:
            response = self.session.get(f"{self.base_url}/features")
            response.raise_for_status()
            
            features = response.json()
            for feature in features:
                if feature['name'].lower().strip() == feature_name.lower().strip():
                    return TaxonomyItem(
                        uuid=feature['id'],
                        name=feature['name'],
                        slug=feature['slug'],
                        item_type='feature'
                    )
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding feature '{feature_name}': {str(e)}")
            return None
    
    def _find_existing_tag(self, tag_name: str) -> Optional[TaxonomyItem]:
        """Find existing tag by name"""
        try:
            response = self.session.get(f"{self.base_url}/tags")
            response.raise_for_status()
            
            tags = response.json()
            for tag in tags:
                if tag['name'].lower().strip() == tag_name.lower().strip():
                    return TaxonomyItem(
                        uuid=tag['id'],
                        name=tag['name'],
                        slug=tag['slug'],
                        item_type='tag'
                    )
            return None
            
        except Exception as e:
            self.logger.error(f"Error finding tag '{tag_name}': {str(e)}")
            return None
    
    def _create_category(self, category_name: str) -> Optional[TaxonomyItem]:
        """Create new category"""
        try:
            # Generate slug from name
            slug = self._generate_slug(category_name)
            
            payload = {
                "name": category_name.strip(),
                "slug": slug,
                "description": f"Auto-generated category for {category_name}"
            }
            
            response = self.session.post(f"{self.base_url}/admin/categories", json=payload)
            response.raise_for_status()
            
            category_data = response.json()
            self.logger.info(f"Created new category: {category_name} (UUID: {category_data['id']})")
            
            return TaxonomyItem(
                uuid=category_data['id'],
                name=category_data['name'],
                slug=category_data['slug'],
                item_type='category'
            )
            
        except Exception as e:
            self.logger.error(f"Error creating category '{category_name}': {str(e)}")
            return None
    
    def _create_feature(self, feature_name: str) -> Optional[TaxonomyItem]:
        """Create new feature"""
        try:
            # Generate slug from name
            slug = self._generate_slug(feature_name)
            
            payload = {
                "name": feature_name.strip(),
                "slug": slug,
                "description": f"Auto-generated feature for {feature_name}"
            }
            
            response = self.session.post(f"{self.base_url}/features/admin", json=payload)
            response.raise_for_status()
            
            feature_data = response.json()
            self.logger.info(f"Created new feature: {feature_name} (UUID: {feature_data['id']})")
            
            return TaxonomyItem(
                uuid=feature_data['id'],
                name=feature_data['name'],
                slug=feature_data['slug'],
                item_type='feature'
            )
            
        except Exception as e:
            self.logger.error(f"Error creating feature '{feature_name}': {str(e)}")
            return None
    
    def _create_tag(self, tag_name: str) -> Optional[TaxonomyItem]:
        """Create new tag"""
        try:
            # Generate slug from name
            slug = self._generate_slug(tag_name)
            
            payload = {
                "name": tag_name.strip(),
                "slug": slug,
                "description": f"Auto-generated tag for {tag_name}"
            }
            
            response = self.session.post(f"{self.base_url}/admin/tags", json=payload)
            response.raise_for_status()
            
            tag_data = response.json()
            self.logger.info(f"Created new tag: {tag_name} (UUID: {tag_data['id']})")
            
            return TaxonomyItem(
                uuid=tag_data['id'],
                name=tag_data['name'],
                slug=tag_data['slug'],
                item_type='tag'
            )
            
        except Exception as e:
            self.logger.error(f"Error creating tag '{tag_name}': {str(e)}")
            return None
    
    def _generate_slug(self, name: str) -> str:
        """Generate URL-friendly slug from name"""
        import re
        
        # Convert to lowercase and replace spaces/special chars with hyphens
        slug = re.sub(r'[^\w\s-]', '', name.lower())
        slug = re.sub(r'[-\s]+', '-', slug)
        slug = slug.strip('-')
        
        # Add timestamp suffix to ensure uniqueness
        timestamp = str(int(time.time()))[-6:]  # Last 6 digits of timestamp
        slug = f"{slug}-{timestamp}"
        
        return slug
    
    def get_taxonomy_uuids(self, taxonomy_result: TaxonomyResult) -> Tuple[List[str], List[str], List[str]]:
        """Extract UUID lists from taxonomy result for entity creation"""
        category_uuids = [item.uuid for item in taxonomy_result.categories]
        feature_uuids = [item.uuid for item in taxonomy_result.features]
        tag_uuids = [item.uuid for item in taxonomy_result.tags]
        
        return category_uuids, feature_uuids, tag_uuids
    
    def refresh_caches(self):
        """Clear all caches to force fresh API calls"""
        self._category_cache.clear()
        self._feature_cache.clear()
        self._tag_cache.clear()
        self.logger.info("Taxonomy caches refreshed")
    
    def get_cache_stats(self) -> Dict[str, int]:
        """Get cache statistics for monitoring"""
        return {
            'categories_cached': len(self._category_cache),
            'features_cached': len(self._feature_cache),
            'tags_cached': len(self._tag_cache)
        } 